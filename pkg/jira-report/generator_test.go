package jirareport

import (
	"testing"
	"time"

	"github.com/ducminhgd/go-atlassian/jira/v3/issue"
)

// TestNoDuplicateIssues tests that issues are not duplicated in the report
func TestNoDuplicateIssues(t *testing.T) {
	// Create a mock generator for testing
	config := &Config{
		JiraHost:      "https://test.atlassian.net",
		JiraUsername:  "<EMAIL>",
		JiraPassword:  "test-token",
		WebhookURL:    "https://test.webhook.url",
		QueryType:     QueryTypeProjectAndHours,
		JiraProject:   "TEST",
		LookbackHours: 24,
		Timezone:      "UTC",
	}

	generator, err := NewGenerator(config)
	if err != nil {
		t.Fatalf("Failed to create generator: %v", err)
	}

	// Create test data with parent-child relationships
	now := time.Now()
	lookbackTime := now.Add(-24 * time.Hour)

	// Create mock issues that could cause duplication
	parentIssue := issue.Issue{
		Key: "TEST-1",
		Fields: issue.IssueFields{
			Summary: "Parent Task",
			Status: issue.StatusDetails{
				Name: "In Progress",
			},
			IssueType: issue.IssueType{
				Name: "Story",
			},
			Parent: issue.ParentIssue{
				Key: "EPIC-1",
			},
			Comment: issue.PagedComment{
				Comments: []issue.IssueComment{
					{
						Created: now.Add(-1 * time.Hour).Format("2006-01-02T15:04:05.000-0700"),
						Author: issue.SimpleUser{
							DisplayName: "Test User",
						},
						Body: "Test comment on parent",
					},
				},
			},
			Worklog: issue.PagedWorklog{
				Worklogs: []issue.Worklog{},
			},
		},
	}

	subTaskIssue := issue.Issue{
		Key: "TEST-2",
		Fields: issue.IssueFields{
			Summary: "Sub Task",
			Status: issue.StatusDetails{
				Name: "To Do",
			},
			IssueType: issue.IssueType{
				Name: "Subtask",
			},
			Parent: issue.ParentIssue{
				Key: "TEST-1", // Parent is TEST-1
			},
			Comment: issue.PagedComment{
				Comments: []issue.IssueComment{
					{
						Created: now.Add(-30 * time.Minute).Format("2006-01-02T15:04:05.000-0700"),
						Author: issue.SimpleUser{
							DisplayName: "Test User",
						},
						Body: "Test comment on subtask",
					},
				},
			},
			Worklog: issue.PagedWorklog{
				Worklogs: []issue.Worklog{},
			},
		},
	}

	// Test the processIssue function
	parentUpdate := generator.processIssue(parentIssue, lookbackTime)
	subTaskUpdate := generator.processIssue(subTaskIssue, lookbackTime)

	// Verify that both issues have updates
	if len(parentUpdate.Updates) == 0 {
		t.Error("Parent issue should have updates")
	}
	if len(subTaskUpdate.Updates) == 0 {
		t.Error("Sub-task issue should have updates")
	}

	// Test that the issue keys are correct
	if parentUpdate.Key != "TEST-1" {
		t.Errorf("Expected parent key to be TEST-1, got %s", parentUpdate.Key)
	}
	if subTaskUpdate.Key != "TEST-2" {
		t.Errorf("Expected subtask key to be TEST-2, got %s", subTaskUpdate.Key)
	}

	// Test that issue types are correct
	if parentUpdate.IssueType != "Story" {
		t.Errorf("Expected parent issue type to be Story, got %s", parentUpdate.IssueType)
	}
	if subTaskUpdate.IssueType != "Subtask" {
		t.Errorf("Expected subtask issue type to be Subtask, got %s", subTaskUpdate.IssueType)
	}
}

// TestProcessIssueWithNoUpdates tests that issues with no recent updates are filtered out
func TestProcessIssueWithNoUpdates(t *testing.T) {
	config := &Config{
		JiraHost:      "https://test.atlassian.net",
		JiraUsername:  "<EMAIL>",
		JiraPassword:  "test-token",
		WebhookURL:    "https://test.webhook.url",
		QueryType:     QueryTypeProjectAndHours,
		JiraProject:   "TEST",
		LookbackHours: 24,
		Timezone:      "UTC",
	}

	generator, err := NewGenerator(config)
	if err != nil {
		t.Fatalf("Failed to create generator: %v", err)
	}

	now := time.Now()
	lookbackTime := now.Add(-24 * time.Hour)

	// Create an issue with old updates (should be filtered out)
	oldIssue := issue.Issue{
		Key: "TEST-OLD",
		Fields: issue.IssueFields{
			Summary: "Old Issue",
			Status: issue.StatusDetails{
				Name: "Done",
			},
			IssueType: issue.IssueType{
				Name: "Task",
			},
			Comment: issue.PagedComment{
				Comments: []issue.IssueComment{
					{
						Created: now.Add(-48 * time.Hour).Format("2006-01-02T15:04:05.000-0700"), // 48 hours ago
						Author: issue.SimpleUser{
							DisplayName: "Test User",
						},
						Body: "Old comment",
					},
				},
			},
			Worklog: issue.PagedWorklog{
				Worklogs: []issue.Worklog{},
			},
		},
	}

	// Process the issue
	issueUpdate := generator.processIssue(oldIssue, lookbackTime)

	// Verify that the issue has no recent updates
	if len(issueUpdate.Updates) != 0 {
		t.Errorf("Expected no updates for old issue, got %d updates", len(issueUpdate.Updates))
	}
}

// TestDuplicateDetection tests that the duplicate detection logic works correctly
func TestDuplicateDetection(t *testing.T) {
	// Create test data that simulates the duplicate scenario
	var noEpicIssues []IssueUpdate
	processedIssues := make(map[string]*IssueUpdate)
	addedToReport := make(map[string]bool)

	// Simulate a parent task being processed first
	parentTask := &IssueUpdate{
		Key:       "DM1-1",
		Summary:   "[Story] - Init project 1-1",
		Status:    "In Progress",
		IssueType: "Story",
		URL:       "https://test.atlassian.net/browse/DM1-1",
		Updates: []Update{
			{
				Time:       time.Now().Add(-1 * time.Hour),
				AuthorName: "James",
				Type:       "comment",
				Content:    "okay",
			},
			{
				Time:       time.Now().Add(-30 * time.Minute),
				AuthorName: "James",
				Type:       "comment",
				Content:    "fine",
			},
		},
		LastUpdated: time.Now().Add(-30 * time.Minute),
		SubTasks:    []IssueUpdate{},
	}

	// Add parent task to processed issues
	processedIssues["DM1-1"] = parentTask

	// Simulate adding a subtask to the parent
	subTask := IssueUpdate{
		Key:       "DM1-3",
		Summary:   "[BE] - BE init 1-1",
		Status:    "To Do",
		IssueType: "Subtask",
		URL:       "https://test.atlassian.net/browse/DM1-3",
		Updates: []Update{
			{
				Time:       time.Now().Add(-15 * time.Minute),
				AuthorName: "James",
				Type:       "comment",
				Content:    "good",
			},
		},
		LastUpdated: time.Now().Add(-15 * time.Minute),
		SubTasks:    []IssueUpdate{},
	}

	// Add subtask to parent
	parentTask.SubTasks = append(parentTask.SubTasks, subTask)

	// Add parent to report if not already added
	if !addedToReport["DM1-1"] {
		noEpicIssues = append(noEpicIssues, *parentTask)
		addedToReport["DM1-1"] = true
	}

	// Mark subtask as processed
	addedToReport["DM1-3"] = true

	// Now simulate the parent task being encountered again in the search results
	// This should NOT create a duplicate
	if !addedToReport["DM1-1"] {
		noEpicIssues = append(noEpicIssues, *parentTask)
		addedToReport["DM1-1"] = true
	}

	// Verify no duplicates
	if len(noEpicIssues) != 1 {
		t.Errorf("Expected 1 issue in noEpicIssues, got %d", len(noEpicIssues))
	}

	// Verify the issue has the subtask
	if len(noEpicIssues[0].SubTasks) != 1 {
		t.Errorf("Expected 1 subtask, got %d", len(noEpicIssues[0].SubTasks))
	}

	// Verify the subtask is correct
	if noEpicIssues[0].SubTasks[0].Key != "DM1-3" {
		t.Errorf("Expected subtask key to be DM1-3, got %s", noEpicIssues[0].SubTasks[0].Key)
	}
}
